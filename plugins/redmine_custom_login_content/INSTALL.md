# Custom Login Content Plugin - 安装指南

## 系统要求

- Redmine 4.0 或更高版本
- Ruby 2.5 或更高版本
- Rails 5.2 或更高版本

## 安装步骤

### 1. 下载插件

将插件文件复制到 Redmine 的 `plugins` 目录：

```bash
cd /path/to/redmine
cp -r /path/to/redmine_custom_login_content plugins/
```

或者如果使用 Git：

```bash
cd /path/to/redmine/plugins
git clone https://github.com/your-repo/redmine_custom_login_content.git
```

### 2. 验证安装

确认插件文件结构正确：

```
plugins/redmine_custom_login_content/
├── init.rb
├── README.md
├── INSTALL.md
├── lib/
│   └── redmine_custom_login_content/
│       └── hooks.rb
├── app/
│   └── views/
│       └── settings/
│           └── _custom_login_content_settings.html.erb
├── config/
│   └── locales/
│       ├── en.yml
│       └── zh.yml
├── assets/
│   └── stylesheets/
│       └── custom_login_content.css
├── test/
│   ├── test_helper.rb
│   └── unit/
│       └── custom_login_content_test.rb
└── examples/
    └── sample_configurations.md
```

### 3. 重启 Redmine

重启 Redmine 服务器以加载插件：

```bash
# 如果使用 Passenger
touch tmp/restart.txt

# 如果使用 Puma
sudo systemctl restart redmine

# 如果使用开发服务器
# 停止服务器 (Ctrl+C) 然后重新启动
rails server
```

### 4. 验证插件加载

1. 以管理员身份登录 Redmine
2. 进入 "管理" → "插件"
3. 确认 "Custom Login Content Plugin" 出现在插件列表中
4. 状态应显示为 "已启用"

## 配置插件

### 1. 访问设置页面

1. 以管理员身份登录 Redmine
2. 进入 "管理" → "插件"
3. 找到 "Custom Login Content Plugin"
4. 点击 "配置" 按钮

### 2. 配置选项

在设置页面中，您可以配置以下选项：

- **文本格式**：选择文本的格式类型
  - Textile：Redmine 默认格式，支持丰富的文本格式
  - Markdown：通用的标记语言
  - HTML：完全的 HTML 支持
  - Plain Text：纯文本，自动转换换行

- **登录框上方文本**：显示在登录表单上方的内容
- **登录框下方文本**：显示在登录表单下方的内容

### 3. 示例配置

#### 基本公告 (Textile 格式)
```textile
h2. 系统维护通知

*重要：* 系统将于本周五 18:00-20:00 进行维护。
请提前保存您的工作。

联系支持：<EMAIL>
```

#### HTML 格式示例
```html
<div style="background: #e8f4fd; padding: 15px; border-radius: 6px;">
  <h3 style="color: #0c5460; margin: 0 0 10px 0;">欢迎使用项目管理系统</h3>
  <p style="margin: 0;">如需帮助，请联系 <a href="mailto:<EMAIL>">技术支持</a></p>
</div>
```

### 4. 保存设置

1. 配置完成后，点击 "应用" 按钮保存设置
2. 访问登录页面查看效果
3. 如需修改，重复上述步骤

## 故障排除

### 插件未出现在插件列表中

**可能原因：**
- 插件文件未正确复制到 plugins 目录
- 文件权限问题
- init.rb 文件有语法错误

**解决方法：**
1. 检查插件目录结构是否正确
2. 确认文件权限允许 Redmine 读取
3. 检查 Redmine 日志文件中的错误信息
4. 重启 Redmine 服务器

### 设置页面无法访问

**可能原因：**
- 用户权限不足
- 设置模板文件路径错误

**解决方法：**
1. 确认以管理员身份登录
2. 检查 `app/views/settings/_custom_login_content_settings.html.erb` 文件是否存在
3. 查看 Redmine 日志文件

### 文本不显示或格式错误

**可能原因：**
- 文本内容为空
- 格式语法错误
- CSS 样式冲突

**解决方法：**
1. 检查设置中的文本内容是否为空
2. 验证 Textile/Markdown/HTML 语法是否正确
3. 使用浏览器开发者工具检查 CSS 样式
4. 尝试使用不同的文本格式

### CSS 样式不生效

**可能原因：**
- CSS 文件未正确加载
- 浏览器缓存问题
- CSS 路径错误

**解决方法：**
1. 清除浏览器缓存
2. 检查 `assets/stylesheets/custom_login_content.css` 文件是否存在
3. 确认 hooks.rb 中的 CSS 加载代码正确
4. 重启 Redmine 服务器

## 卸载插件

如需卸载插件：

1. 停止 Redmine 服务器
2. 删除插件目录：
   ```bash
   rm -rf plugins/redmine_custom_login_content
   ```
3. 重启 Redmine 服务器

**注意：** 卸载插件后，所有相关设置将丢失。

## 技术支持

如果遇到问题，请：

1. 查看 Redmine 日志文件 (`log/production.log` 或 `log/development.log`)
2. 检查插件的 GitHub Issues 页面
3. 联系插件开发者

## 开发和贡献

如果您想为插件开发做贡献：

1. Fork 项目仓库
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

请确保：
- 代码符合 Ruby 和 Rails 规范
- 包含适当的测试
- 更新相关文档
