# Custom Login Content Plugin for Redmine

## 概述

Custom Login Content Plugin 是一款为 Redmine 平台定制登录页面内容的功能插件，通过预定义文本配置，帮助管理员在登录界面添加自定义说明、提示或公告内容，提升用户引导效率与系统使用体验。

## 主要功能

### 双区域自定义文本配置
- **登录框上方文本**：支持在登录表单顶部添加醒目提示（如系统维护通知、使用须知等）
- **登录框下方文本**：可在表单底部添加辅助信息（如账号申请流程、技术支持联系方式）

### 内容编辑与格式支持
- **多种文本格式**：支持 Textile、Markdown、HTML 和纯文本格式
- **富文本功能**：支持标题、列表、链接、加粗等格式化内容
- **实时预览**：在设置页面提供预览功能，方便管理员查看效果
- **管理界面**：通过插件后台管理界面直接配置，无需修改 Redmine 代码

## 安装方法

1. 将插件文件复制到 Redmine 的 `plugins` 目录：
   ```bash
   cd /path/to/redmine
   git clone https://github.com/your-repo/redmine_custom_login_content.git plugins/redmine_custom_login_content
   ```

2. 重启 Redmine 服务器：
   ```bash
   # 如果使用 Passenger
   touch tmp/restart.txt
   
   # 如果使用其他服务器
   sudo service redmine restart
   ```

3. 登录 Redmine 管理界面，进入 "管理" → "插件" 确认插件已正确加载

## 配置方法

1. 以管理员身份登录 Redmine
2. 进入 "管理" → "插件"
3. 找到 "Custom Login Content Plugin" 并点击 "配置"
4. 在配置页面设置：
   - **文本格式**：选择 Textile、Markdown、HTML 或纯文本
   - **登录框上方文本**：输入要显示在登录表单上方的内容
   - **登录框下方文本**：输入要显示在登录表单下方的内容
5. 使用预览功能查看效果
6. 点击 "应用" 保存设置

## 应用场景举例

### 内部系统公告
在登录框上方显示：
```textile
h2. 系统维护通知

*重要提醒：* 本周系统维护时间：周五 18:00-20:00
请在此时间前保存您的工作。

如有紧急问题，请联系 IT 部门：<EMAIL>
```

### 新用户指引
在登录框下方添加：
```textile
h3. 首次登录？

* 新员工请联系 IT 部门获取账号
* 外部用户请通过 "公司门户":http://portal.company.com 申请
* 技术支持：(555) 123-4567

_登录即表示您同意 "系统使用协议":http://company.com/terms_
```

### 合规与安全提示
```html
<div style="background: #fff3cd; padding: 10px; border-radius: 4px;">
  <strong>安全提醒：</strong>
  <ul>
    <li>请勿在公共计算机上保存密码</li>
    <li>发现异常登录请立即联系管理员</li>
    <li>定期更新您的密码以确保账户安全</li>
  </ul>
</div>
```

## 文本格式说明

### Textile 格式
- 标题：`h1. 一级标题`、`h2. 二级标题`
- 加粗：`*粗体文本*`
- 斜体：`_斜体文本_`
- 链接：`"链接文本":http://example.com`
- 列表：`* 列表项`

### Markdown 格式
- 标题：`# 一级标题`、`## 二级标题`
- 加粗：`**粗体文本**`
- 斜体：`*斜体文本*`
- 链接：`[链接文本](http://example.com)`
- 列表：`- 列表项`

### HTML 格式
支持完整的 HTML 标签，可以实现复杂的样式和布局。

### 纯文本
按原样显示，自动转换换行符为 `<br>` 标签。

## 样式自定义

插件提供了基本的 CSS 样式类：
- `.custom-login-text`：所有自定义文本的基础样式
- `.login-top-text`：登录框上方文本的样式
- `.login-bottom-text`：登录框下方文本的样式

您可以通过 Redmine 的主题或自定义 CSS 来修改这些样式。

## 技术特性

- **Hook 系统**：使用 Redmine 的 `view_account_login_top` 和 `view_account_login_bottom` hooks
- **设置存储**：使用 Redmine 的插件设置系统安全存储配置
- **多语言支持**：支持中文和英文界面
- **兼容性**：兼容 Redmine 4.0+ 版本

## 故障排除

### 插件未显示
1. 确认插件文件已正确复制到 `plugins` 目录
2. 检查 Redmine 日志文件是否有错误信息
3. 重启 Redmine 服务器

### 文本格式不正确
1. 检查选择的文本格式是否正确
2. 确认 Textile/Markdown 语法是否正确
3. 使用预览功能检查效果

### 样式显示异常
1. 检查是否有 CSS 冲突
2. 确认主题兼容性
3. 使用浏览器开发者工具检查样式

## 版本历史

- **v1.0.0**：初始版本
  - 支持登录框上方和下方文本配置
  - 支持多种文本格式
  - 提供管理界面和预览功能
  - 多语言支持

## 许可证

本插件基于 GPL v2 许可证发布。

## 支持

如有问题或建议，请通过以下方式联系：
- GitHub Issues: https://github.com/your-repo/redmine_custom_login_content/issues
- Email: <EMAIL>

## 贡献

欢迎提交 Pull Request 或报告 Bug。请确保：
1. 代码符合 Redmine 插件开发规范
2. 包含适当的测试
3. 更新相关文档
