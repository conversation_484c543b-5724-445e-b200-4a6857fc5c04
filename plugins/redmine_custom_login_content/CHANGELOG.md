# Changelog

All notable changes to the Custom Login Content Plugin will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-10

### Added
- Initial release of Custom Login Content Plugin
- Support for custom text above and below login form
- Multiple text format support:
  - Textile formatting
  - Markdown (CommonMark) formatting  
  - HTML formatting
  - Plain text formatting
- Admin configuration interface with live preview
- Multi-language support (English and Chinese)
- Responsive CSS styling with theme support
- Security features:
  - HTML sanitization for safe content
  - XSS protection
- Integration with Redmine's text formatting system
- Comprehensive test suite
- Detailed documentation and examples

### Features
- **Dual Text Areas**: Configure text for both above and below login form
- **Rich Text Support**: Full support for Textile, Markdown, HTML, and plain text
- **Live Preview**: Real-time preview in admin settings
- **Responsive Design**: Mobile-friendly styling
- **Theme Compatibility**: Support for light and dark themes
- **Security**: Built-in XSS protection and HTML sanitization
- **Internationalization**: English and Chinese language support
- **Easy Installation**: Simple plugin installation process

### Technical Details
- Compatible with Redmine 4.0+
- Uses Redmine's hook system for clean integration
- Leverages Redmine's built-in text formatting engines
- Follows Redmine plugin development best practices
- Includes comprehensive unit tests

### Documentation
- Complete installation guide
- Configuration examples
- Sample text configurations
- Troubleshooting guide
- Development documentation

## [Unreleased]

### Planned Features
- Additional language support
- More text formatting options
- Advanced styling options
- User-specific text display
- Scheduled text changes
- Integration with Redmine's announcement system

---

## Version History

### Version 1.0.0 (2024-01-10)
- Initial stable release
- Core functionality complete
- Full documentation provided
- Ready for production use
