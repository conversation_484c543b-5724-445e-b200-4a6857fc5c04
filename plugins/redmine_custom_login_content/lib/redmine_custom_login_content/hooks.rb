module RedmineCustomLoginText
  class Hooks < Redmine::Hook::ViewListener
    include ApplicationHelper

    # Hook to include CSS in the HTML head
    def view_layouts_base_html_head(context={})
      stylesheet_link_tag('custom_login_content', :plugin => 'redmine_custom_login_content')
    end

    # Hook for content above the login form
    def view_account_login_top(context={})
      settings = Setting.plugin_redmine_custom_login_content
      return '' if settings['login_top_text'].blank?

      formatted_text = format_custom_text(settings['login_top_text'], settings['text_format'])

      content_tag(:div, formatted_text, :class => 'custom-login-text login-top-text')
    end

    # Hook for content below the login form
    def view_account_login_bottom(context={})
      settings = Setting.plugin_redmine_custom_login_content
      return '' if settings['login_bottom_text'].blank?

      formatted_text = format_custom_text(settings['login_bottom_text'], settings['text_format'])

      content_tag(:div, formatted_text, :class => 'custom-login-text login-bottom-text')
    end

    private

    def format_custom_text(text, format)
      case format
      when 'html'
        # For HTML format, sanitize but allow safe HTML tags
        sanitize(text, :tags => %w[p br strong b em i u h1 h2 h3 h4 h5 h6 ul ol li a div span],
                      :attributes => %w[href title class style])
      when 'plain'
        # Plain text with line breaks converted to <br>
        simple_format(h(text))
      else
        # Use Redmine's textilizable method for textile, markdown, and other formats
        # This will respect the current Redmine text formatting setting
        with_text_formatting(format) do
          textilizable(text, :formatting => true)
        end
      end
    end

    def with_text_formatting(format)
      original_formatting = Setting.text_formatting
      begin
        # Temporarily set the text formatting to the desired format
        Setting.text_formatting = format if %w[textile common_mark markdown].include?(format)
        yield
      ensure
        # Restore original formatting
        Setting.text_formatting = original_formatting
      end
    end
  end
end
