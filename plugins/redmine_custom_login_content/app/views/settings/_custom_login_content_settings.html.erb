<div class="box tabular settings custom-login-text-settings">
  <h3><%= l(:label_custom_login_content_settings) %></h3>

  <p>
    <label for="settings_text_format"><%= l(:label_text_format) %></label>
    <%= select_tag 'settings[text_format]',
        options_for_select([
          [l(:label_format_textile), 'textile'],
          [l(:label_format_markdown), 'common_mark'],
          [l(:label_format_html), 'html'],
          [l(:label_format_plain), 'plain']
        ], settings['text_format']),
        :id => 'settings_text_format' %>
    <em class="info"><%= l(:text_format_description) %></em>
  </p>

  <p>
    <label for="settings_login_top_text"><%= l(:label_login_top_text) %></label>
    <%= text_area_tag 'settings[login_top_text]', settings['login_top_text'],
        :rows => 6, :cols => 80, :class => 'wiki-edit',
        :id => 'settings_login_top_text' %>
    <em class="info"><%= l(:text_login_top_description) %></em>
  </p>

  <p>
    <label for="settings_login_bottom_text"><%= l(:label_login_bottom_text) %></label>
    <%= text_area_tag 'settings[login_bottom_text]', settings['login_bottom_text'],
        :rows => 6, :cols => 80, :class => 'wiki-edit',
        :id => 'settings_login_bottom_text' %>
    <em class="info"><%= l(:text_login_bottom_description) %></em>
  </p>

  <div class="custom-login-text-preview">
    <h4><%= l(:label_preview) %></h4>
    <div class="preview-container">
      <div id="login-top-preview" class="custom-login-text login-top-text">
        <!-- Preview content will be populated by JavaScript -->
      </div>
      <div class="login-form-placeholder">
        <div style="border: 2px dashed #ccc; padding: 20px; text-align: center; margin: 10px 0;">
          <%= l(:text_login_form_placeholder) %>
        </div>
      </div>
      <div id="login-bottom-preview" class="custom-login-text login-bottom-text">
        <!-- Preview content will be populated by JavaScript -->
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  function updatePreview() {
    var topText = document.getElementById('settings_login_top_text').value;
    var bottomText = document.getElementById('settings_login_bottom_text').value;
    var format = document.getElementById('settings_text_format').value;

    var topPreview = document.getElementById('login-top-preview');
    var bottomPreview = document.getElementById('login-bottom-preview');

    function renderText(text, format) {
      if (!text.trim()) return '';

      switch(format) {
        case 'html':
          return text;
        case 'textile':
        case 'common_mark':
          // For textile and markdown, we'll make an AJAX call to format the text
          // This is a placeholder - you'll need to implement the actual AJAX call
          return '<em>Formatting preview not available for ' + format + '</em><br>' + escapeHtml(text);
        case 'plain':
        default:
          return escapeHtml(text);
      }
    }

    function createIframe(content) {
      var iframe = document.createElement('iframe');
      iframe.style.width = '100%';
      iframe.style.border = 'none';
      iframe.style.margin = '0';
      
      // Wait for iframe to load before accessing its document
      iframe.onload = function() {
        var doc = iframe.contentDocument || iframe.contentWindow.document;
        doc.open();
        doc.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <style>
              body { margin: 10px; font-family: Arial, sans-serif; }
              .preview-content { line-height: 1.5; }
            </style>
          </head>
          <body>
            <div class="preview-content">${content}</div>
          </body>
          </html>
        `);
        doc.close();
      };
      
      return iframe;
    }

    if (topText.trim()) {
      topPreview.innerHTML = '';
      topPreview.appendChild(createIframe('<strong>Top Text Preview:</strong><br>' + renderText(topText, format)));
      topPreview.style.display = 'block';
    } else {
      topPreview.style.display = 'none';
    }

    if (bottomText.trim()) {
      bottomPreview.innerHTML = '';
      bottomPreview.appendChild(createIframe('<strong>Bottom Text Preview:</strong><br>' + renderText(bottomText, format)));
      bottomPreview.style.display = 'block';
    } else {
      bottomPreview.style.display = 'none';
    }
  }

  function escapeHtml(text) {
    var div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML.replace(/\n/g, '<br>');
  }

  // Add event listeners
  document.getElementById('settings_login_top_text').addEventListener('input', updatePreview);
  document.getElementById('settings_login_bottom_text').addEventListener('input', updatePreview);
  document.getElementById('settings_text_format').addEventListener('change', updatePreview);

  // Initial preview
  updatePreview();
});
</script>
