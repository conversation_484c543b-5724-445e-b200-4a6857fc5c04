require File.expand_path('../../test_helper', __FILE__)

class CustomLoginTextTest < ActiveSupport::TestCase
  include ApplicationHelper
  
  def setup
    @original_settings = Setting.plugin_redmine_custom_login_content.dup
  end
  
  def teardown
    Setting.plugin_redmine_custom_login_content = @original_settings
  end
  
  def test_plugin_should_be_registered
    assert Redmine::Plugin.registered_plugins.key?(:redmine_custom_login_content)
  end
  
  def test_plugin_should_have_settings
    plugin = Redmine::Plugin.find(:redmine_custom_login_content)
    assert plugin.configurable?
    assert_equal 'settings/custom_login_content_settings', plugin.settings[:partial]
  end
  
  def test_hooks_should_return_empty_when_no_text_configured
    Setting.plugin_redmine_custom_login_content = {
      'login_top_text' => '',
      'login_bottom_text' => '',
      'text_format' => 'textile'
    }
    
    hook = RedmineCustomLoginText::Hooks.instance
    assert_equal '', hook.view_account_login_top
    assert_equal '', hook.view_account_login_bottom
  end
  
  def test_hooks_should_format_textile_text
    Setting.plugin_redmine_custom_login_content = {
      'login_top_text' => 'h2. Test Title\n\n*Bold text*',
      'login_bottom_text' => '_Italic text_',
      'text_format' => 'textile'
    }
    
    hook = RedmineCustomLoginText::Hooks.instance
    
    top_result = hook.view_account_login_top
    assert_match /custom-login-text/, top_result
    assert_match /login-top-text/, top_result
    
    bottom_result = hook.view_account_login_bottom
    assert_match /custom-login-text/, bottom_result
    assert_match /login-bottom-text/, bottom_result
  end
  
  def test_hooks_should_format_html_text
    Setting.plugin_redmine_custom_login_content = {
      'login_top_text' => '<h2>Test Title</h2><p><strong>Bold text</strong></p>',
      'login_bottom_text' => '<p><em>Italic text</em></p>',
      'text_format' => 'html'
    }
    
    hook = RedmineCustomLoginText::Hooks.instance
    
    top_result = hook.view_account_login_top
    assert_match /<h2>Test Title<\/h2>/, top_result
    assert_match /<strong>Bold text<\/strong>/, top_result
    
    bottom_result = hook.view_account_login_bottom
    assert_match /<em>Italic text<\/em>/, bottom_result
  end
  
  def test_hooks_should_format_plain_text
    Setting.plugin_redmine_custom_login_content = {
      'login_top_text' => "Line 1\nLine 2",
      'login_bottom_text' => "Simple text",
      'text_format' => 'plain'
    }
    
    hook = RedmineCustomLoginText::Hooks.instance
    
    top_result = hook.view_account_login_top
    assert_match /Line 1/, top_result
    assert_match /Line 2/, top_result
    assert_match /<br \/>/, top_result  # Should convert newlines to <br>
    
    bottom_result = hook.view_account_login_bottom
    assert_match /Simple text/, bottom_result
  end
  
  def test_hooks_should_sanitize_html_input
    Setting.plugin_redmine_custom_login_content = {
      'login_top_text' => '<script>alert("xss")</script><h2>Safe Title</h2>',
      'login_bottom_text' => '<p>Safe text</p>',
      'text_format' => 'html'
    }
    
    hook = RedmineCustomLoginText::Hooks.instance
    
    top_result = hook.view_account_login_top
    assert_no_match /<script>/, top_result  # Script tags should be removed
    assert_match /<h2>Safe Title<\/h2>/, top_result  # Safe tags should remain
  end
end
