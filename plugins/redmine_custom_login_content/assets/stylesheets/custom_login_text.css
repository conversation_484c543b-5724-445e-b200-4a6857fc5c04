/* Custom Login Content Plugin Styles */

.custom-login-text {
  font-size: 14px;
}

.custom-login-text h1,
.custom-login-text h2,
.custom-login-text h3,
.custom-login-text h4,
.custom-login-text h5,
.custom-login-text h6 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.custom-login-text p {
  margin-bottom: 10px;
}

.custom-login-text ul,
.custom-login-text ol {
  margin-bottom: 10px;
  padding-left: 20px;
}

.custom-login-text a {
  color: #169;
  text-decoration: none;
}

.custom-login-text a:hover {
  color: #0d4f8c;
  text-decoration: underline;
}

.login-top-text {
  background-color: #e8f4fd;
  color: #0c5460;
}

.login-top-text h1,
.login-top-text h2,
.login-top-text h3 {
  color: #0c5460;
}

.login-bottom-text {
  background-color: #f8f9fa;
  color: #495057;
}

.login-bottom-text h1,
.login-bottom-text h2,
.login-bottom-text h3 {
  color: #495057;
}

/* Responsive design */
@media (max-width: 768px) {
  .custom-login-text {
    margin: 10px 0;
    padding: 10px;
    font-size: 13px;
  }
}

/* Settings page preview styles */
.custom-login-text-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.custom-login-text-preview h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
  font-size: 16px;
}

.preview-container {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 20px;
  margin-top: 10px;
}

.login-form-placeholder {
  margin: 20px 0;
  padding: 30px;
  border: 2px dashed #ccc;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #fafafa;
  border-radius: 4px;
}

/* Settings form improvements */
.custom-login-text-settings .wiki-edit {
  width: 100%;
  max-width: 600px;
  min-height: 120px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px;
}

.custom-login-text-settings .wiki-edit:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.custom-login-text-settings em.info {
  display: block;
  margin-top: 5px;
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.custom-login-text-settings label {
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.custom-login-text-settings select {
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
}

/* Dark theme support */
body.theme-dark .custom-login-text {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

body.theme-dark .login-top-text {
  background-color: #2c5282;
  border-color: #3182ce;
  color: #bee3f8;
}

body.theme-dark .login-bottom-text {
  background-color: #276749;
  border-color: #38a169;
  color: #c6f6d5;
}
