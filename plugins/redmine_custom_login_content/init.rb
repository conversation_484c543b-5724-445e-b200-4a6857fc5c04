require 'redmine'

Rails.logger.info 'Starting Custom Login Content plugin for Redmine'

Redmine::Plugin.register :redmine_custom_login_content do
  name 'Custom Login Content Plugin'
  author 'AiYuHang'
  description 'A plugin to add custom text above and below the login form with rich text support'
  version '1.0.0'
  url 'https://redminecn.com'
  author_url 'mailto:<EMAIL>'

  # Plugin settings with default values
  settings :default => {
    'login_top_text' => '',
    'login_bottom_text' => '',
    'text_format' => 'textile'  # textile, common_mark, html, plain
  }, :partial => 'settings/custom_login_content_settings'
end

# Load the hooks
require_relative 'lib/redmine_custom_login_content/hooks'
